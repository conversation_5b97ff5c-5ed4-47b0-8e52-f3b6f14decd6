import { Stack } from 'expo-router';
import { Inter_900Black, useFonts } from '@expo-google-fonts/inter';
import * as SplashScreen from 'expo-splash-screen';
import { useEffect } from 'react';
import { ThemeProvider } from '../context/ThemeContext';
import { SafeAreaView } from 'react-native-safe-area-context';
import Toast from 'react-native-toast-message';
import ToastConfig from './Components/Shared/ToastConfig';

SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
    const [loaded, error] = useFonts({
        Inter: Inter_900Black,
        playwrite: require('../assets/fonts/PlaywriteDKLoopet-Thin.ttf'),
    });

    useEffect(() => {
        const hideSplash = async () => {
            if (loaded || error) {
                try {
                    await SplashScreen.hideAsync();
                } catch (e) {
                    console.error(e);
                }
            }
        };
        hideSplash();
    }, [loaded, error]);

    if (!loaded && !error) {
        return null;
    }

    return (
        <ThemeProvider>
            <SafeAreaView style={{ flex: 1 }}>
                <Stack screenOptions={{ headerShown: false }} />
                <Toast config={ToastConfig} />
            </SafeAreaView>
        </ThemeProvider>
    );
}
