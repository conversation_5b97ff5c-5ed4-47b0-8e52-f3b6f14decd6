import { Linking } from 'react-native';
import CategorySection from '../Shared/CategorySection';

const brokers = [
    {
        id: '1',
        name: 'Premier Realty',
        description: 'Connecting buyers and sellers with premium properties',
        avatar: require('../../../assets/images/profile.png'),
        rating: 3.5,
        projects: 'Ex cemsja sjsbh',
        roleLabel: 'broker',
    },
    {
        id: '2',
        title: 'City Properties',
        description: 'Expert brokers specializing in urban real estate',
        image: require('../../../assets/images/image2.png'),
        url: 'https://example.com/broker2',
    },
    {
        id: '3',
        title: 'Golden Homes',
        description: 'Luxury property specialists with exclusive listings',
        image: require('../../../assets/images/image3.png'),
        url: 'https://example.com/broker3',
    },
    {
        id: '4',
        title: 'Smart Realty',
        description: 'Technology-driven brokerage for modern clients',
        image: require('../../../assets/images/icon.png'),
        url: 'https://example.com/broker4',
    },
];

export default function Brokers() {
    const openBroker = (url) => {
        Linking.openURL(url);
    };

    return (
        <CategorySection
            title="Featured Brokers"
            role="broker"
            data={brokers}
            onItemPress={openBroker}
            viewAllRoute="/Brokers/BrokerList"
        />
    );
}
