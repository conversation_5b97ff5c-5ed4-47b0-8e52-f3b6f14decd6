import React, { useEffect, useState, useContext } from 'react';
import {
    ScrollView,
    View,
    Text,
    Image,
    TouchableOpacity,
    StyleSheet,
    Dimensions,
    Vibration,
    ActivityIndicator,
    Modal,
    StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { privateAPIClient } from '../../api/';
import BackButton from '../Components/Shared/BackButton';
import { ThemeContext } from '../../context/ThemeContext';
import { showToast } from '../../utils/showToast';

const { height } = Dimensions.get('window');

const PROFILE_OPTIONS = [
    {
        key: 'professional',
        icon: 'briefcase-outline',
        label: 'Professional Details',
        visibleto: ['contractor', 'site scout'],
        url: '/Profile/Professional',
    },
    {
        key: 'applications',
        icon: 'receipt-outline',
        label: 'Manage Applications',
        visibleto: ['user', 'contractor', 'site scout'],
        url: '/Profile/Applications',
    },
    {
        key: 'payments',
        icon: 'card-outline',
        label: 'Manage Payments',
        visibleto: ['user', 'contractor', 'site scout'],
        url: '/Profile/Payments',
    },
    {
        key: 'hiring',
        icon: 'hourglass-outline',
        label: 'Hiring History',
        visibleto: ['user'],
        url: '/Profile/Hiring',
    },
    {
        key: 'tickets',
        icon: 'chatbubbles-outline',
        label: 'Raise Tickets',
        visibleto: ['user', 'contractor', 'site scout'],
        url: '/Profile/Tickets',
    },
    {
        key: 'settings',
        icon: 'settings-outline',
        label: 'Settings',
        visibleto: ['user', 'contractor', 'site scout'],
        url: '/Profile/Settings',
    },
    {
        key: 'logout',
        icon: 'log-out-outline',
        label: 'Logout',
        visibleto: ['user', 'contractor', 'site scout'],
        url: '/auth/Login',
    },
];

export default function ProfileSection() {
    const { theme, isDarkMode, toggleTheme } = useContext(ThemeContext);
    const router = useRouter();
    const [userData, setUserData] = useState({
        name: '',
        email: '',
        phone: '',
        avatarUri: null,
        role: '',
    });
    const [isFetchingUserData, setIsFetchingUserData] = useState(true);
    const [isExtendCard, setExtendCard] = useState(false);

    useEffect(() => {
        const fetchUserDetails = async () => {
            try {
                const response = await privateAPIClient.get(
                    '/user-service/api/v1/user/profile'
                );
                const data = response.data.user;
                setUserData({
                    name: data.name || '',
                    email: data.email || '',
                    phone: data.phone || '',
                    avatarUri: data.avatar || null,
                    role: data.role || 'user',
                });
                Vibration.vibrate(100);
            } catch (error) {
                console.error('Fetch user error:', error);
                showToast('error', 'Error', 'Failed to fetch user details.');
                Vibration.vibrate(200);
            } finally {
                setIsFetchingUserData(false);
            }
        };
        fetchUserDetails();
    }, []);

    const handleLogout = async () => {
        try {
            await AsyncStorage.removeItem('authToken');
            showToast({
                text1: 'Logout Successful',
                text2: 'You have been logged out successfully.',
            });
            router.replace('../auth/Login');
        } catch (error) {
            console.error('Logout error:', error);
        }
    };

    const handleOptionPress = (url, key) => {
        // Vibration.vibrate(50);
        if (key === 'logout') {
            handleLogout();
        } else {
            router.push(url);
        }
    };

    const handleThemeToggle = () => {
        toggleTheme();
        if (isDarkMode) {
            showToast('info', 'Light Mode', 'You have enabled light mode.');
        } else {
            showToast('info', 'Dark Mode', 'You have enabled dark mode.');
        }
    };

    const filteredOptions = PROFILE_OPTIONS.filter((option) =>
        option.visibleto.includes(userData.role.toLowerCase())
    );

    if (isFetchingUserData) {
        return (
            <View
                style={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                    backgroundColor: theme.BACKGROUND,
                }}
            >
                <ActivityIndicator size="large" color={theme.PRIMARY} />
            </View>
        );
    }

    return (
        <View style={{ flex: 1, backgroundColor: theme.BACKGROUND }}>
            <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
                backgroundColor={theme.PRIMARY}
            />
            <ScrollView
                contentContainerStyle={{ flexGrow: 1, minHeight: height }}
            >
                <BackButton color={theme.WHITE} />

                <View
                    style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        minHeight: height * 0.5, // Use minHeight instead of height
                        zIndex: -1,
                    }}
                >
                    <Image
                        source={require('../../assets/images/background.png')}
                        style={{ width: '100%', height: '100%' }}
                        resizeMode="cover"
                    />
                    <LinearGradient
                        colors={['rgba(42, 142, 158, 0.7)', theme.PRIMARY]}
                        style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                        }}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 0, y: 1 }}
                    />
                </View>

                <View style={{ flex: 1, alignItems: 'center' }}>
                    <View
                        style={{
                            width: '90%',
                            maxWidth: 400,
                            backgroundColor: theme.CARD,
                            borderRadius: 20,
                            padding: 24,
                            elevation: 5,
                        }}
                    >
                        <View
                            style={{
                                alignItems: 'center',
                                position: 'relative',
                            }}
                        >
                            <TouchableOpacity
                                style={{
                                    position: 'absolute',
                                    top: -16,
                                    right: -16,
                                    padding: 8,
                                    backgroundColor: theme.PRIMARY + '25',
                                    borderRadius: 20,
                                }}
                                onPress={handleThemeToggle}
                                accessibilityLabel="Toggle Dark Mode"
                            >
                                <Ionicons
                                    name={isDarkMode ? 'moon' : 'moon-outline'}
                                    size={24}
                                    color={theme.PRIMARY}
                                />
                            </TouchableOpacity>
                            <View
                                style={{
                                    flexDirection: 'column',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                }}
                            >
                                <View
                                    style={{
                                        backgroundColor: theme.PRIMARY,
                                        borderRadius: 40,
                                        width: 80,
                                        height: 80,
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        overflow: 'hidden',
                                        marginBottom: 16,
                                    }}
                                >
                                    {userData.avatarUri ? (
                                        <Image
                                            source={{ uri: userData.avatarUri }}
                                            style={{
                                                width: '100%',
                                                height: '100%',
                                            }}
                                        />
                                    ) : (
                                        <Text
                                            style={{
                                                color: theme.WHITE,
                                                fontSize: 30,
                                                fontWeight: 'bold',
                                            }}
                                        >
                                            {userData.name
                                                .charAt(0)
                                                .toUpperCase()}
                                        </Text>
                                    )}
                                </View>

                                <View
                                    style={{
                                        flexDirection: 'column',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        width: '100%',
                                    }}
                                >
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            justifyContent: 'center',
                                            gap: 3,
                                            alignItems: 'center',
                                            marginBottom: 8,
                                        }}
                                    >
                                        <Text
                                            style={{
                                                fontSize: 24,
                                                fontWeight: 'bold',
                                                textAlign: 'center',
                                                color: theme.TEXT_PRIMARY,
                                                marginRight: 8,
                                            }}
                                        >
                                            {userData.name || 'User'}
                                        </Text>
                                        <TouchableOpacity
                                            onPress={() => {
                                                Vibration.vibrate(50);
                                                setExtendCard(true);
                                            }}
                                            accessibilityLabel="Extend Profile Settings"
                                        >
                                            <Ionicons
                                                name="chevron-down-outline"
                                                size={20}
                                                color={theme.PRIMARY}
                                            />
                                        </TouchableOpacity>
                                    </View>
                                    <Text
                                        style={{
                                            fontSize: 16,
                                            color: theme.TEXT_SECONDARY,
                                            marginBottom: 4,
                                        }}
                                    >
                                        {userData.email || 'No email provided'}
                                    </Text>
                                    <Text
                                        style={{
                                            fontSize: 16,
                                            color: theme.TEXT_SECONDARY,
                                        }}
                                    >
                                        {userData.phone || 'No phone provided'}
                                    </Text>
                                </View>
                            </View>
                        </View>

                        <View
                            style={{
                                borderTopWidth: StyleSheet.hairlineWidth,
                                borderTopColor: theme.INPUT_BORDER,
                                paddingTop: 16,
                            }}
                        >
                            {filteredOptions.map(
                                ({ key, icon, label, url }) => (
                                    <TouchableOpacity
                                        key={key}
                                        style={{
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            justifyContent: 'space-between',
                                            paddingVertical: 15,
                                            borderBottomWidth:
                                                StyleSheet.hairlineWidth,
                                            borderBottomColor:
                                                theme.INPUT_BORDER,
                                        }}
                                        onPress={() =>
                                            handleOptionPress(url, key)
                                        }
                                        accessibilityLabel={label}
                                        accessibilityRole="button"
                                        testID={`${key}-option`}
                                    >
                                        <View
                                            style={{
                                                flexDirection: 'row',
                                                alignItems: 'center',
                                            }}
                                        >
                                            <Ionicons
                                                name={icon}
                                                size={22}
                                                color={theme.PRIMARY}
                                            />
                                            <Text
                                                style={{
                                                    marginLeft: 15,
                                                    fontSize: 16,
                                                    color: theme.TEXT_PRIMARY,
                                                }}
                                            >
                                                {label}
                                            </Text>
                                        </View>
                                        <Ionicons
                                            name="chevron-forward-outline"
                                            size={20}
                                            color={theme.TEXT_SECONDARY}
                                        />
                                    </TouchableOpacity>
                                )
                            )}
                        </View>
                    </View>
                </View>

                <Modal
                    visible={isExtendCard}
                    animationType="slide"
                    transparent
                    onRequestClose={() => setExtendCard(false)}
                >
                    <View
                        style={{
                            backgroundColor: 'rgba(0, 0, 0, 0.5)',
                            justifyContent: 'center',
                            alignItems: 'center',
                        }}
                    ></View>
                </Modal>
            </ScrollView>
        </View>
    );
}
