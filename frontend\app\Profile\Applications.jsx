import React, { useEffect, useState, useContext } from 'react';
import {
    ScrollView,
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    Dimensions,
    Vibration,
    ActivityIndicator,
    StatusBar,
    Image,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { privateAPIClient } from '../../api/';
import BackButton from '../Components/Shared/BackButton';
import DocumentPreviewModal from '../Components/Profile/DocumentPreviewModal';
import { ThemeContext } from '../../context/ThemeContext';
import { showToast } from '../../utils/showToast';

const { height } = Dimensions.get('window');

export default function Applications() {
    const { theme, isDarkMode } = useContext(ThemeContext);
    const router = useRouter();
    const [applications, setApplications] = useState({
        brokers: [],
        contractors: [],
    });
    const [user, setUser] = useState({ name: '', role: 'user' });
    const [isFetching, setIsFetching] = useState(true);
    const [modalVisible, setModalVisible] = useState(false);
    const [modalDocument, setModalDocument] = useState({ url: '', type: '' });

    // Fetch user and applications
    useEffect(() => {
        const fetchData = async () => {
            try {
                // Fetch user profile
                const userResponse = await privateAPIClient.get(
                    '/user-service/api/v1/user/profile'
                );
                const userData = userResponse.data.user;
                setUser({
                    name: userData.name || '',
                    role: userData.role || 'user',
                });

                // Fetch applications (mock data)
                const response = {
                    data: {
                        brokers: [
                            {
                                id: '1234567890',
                                aadhaarNumber: '123456789012',
                                nameOnAadhaar: 'John Doe',
                                dateOfBirth: '1990-05-15T00:00:00.000Z',
                                address: '123 Main St, Mumbai, Maharashtra',
                                panNumber: '**********',
                                panName: 'John Doe',
                                panDateOfBirth: '1990-05-15T00:00:00.000Z',
                                status: 'pending',
                                aadhaarDocument:
                                    'https://example.com/broker1/aadhaar.png', // Fixed invalid URL
                                panDocument:
                                    'https://example.com/broker1/pan.png',
                            },
                            {
                                id: '0987654321',
                                aadhaarNumber: '************',
                                nameOnAadhaar: 'Jane Smith',
                                dateOfBirth: '1992-07-20T00:00:00.000Z',
                                address: '456 Park Ave, Delhi',
                                panNumber: '**********',
                                panName: 'Jane Smith',
                                panDateOfBirth: '1992-07-20T00:00:00.000Z',
                                status: 'approved',
                                aadhaarDocument:
                                    'https://example.com/broker2/aadhaar.png',
                                panDocument:
                                    'https://example.com/broker2/pan.png',
                            },
                        ],
                        contractors: [
                            {
                                id: '9876543210',
                                aadhaarNumber: '************',
                                nameOnAadhaar: 'Jane Smith',
                                dateOfBirth: '1992-07-20T00:00:00.000Z',
                                address: '456 Park Ave, Delhi',
                                panNumber: '**********',
                                panName: 'Jane Smith',
                                panDateOfBirth: '1992-07-20T00:00:00.000Z',
                                gstin: '12**********1Z5',
                                businessName: 'Smith Constructions',
                                experience: 10,
                                expertiseAreas: 'Residential, Commercial',
                                pastProjects:
                                    'Sunset Villas, Tech Park Phase 2',
                                status: 'pending',
                                aadhaarDocument:
                                    'https://example.com/contractor1/aadhaar.png',
                                panDocument:
                                    'https://example.com/contractor1/pan.png',
                                gstinDocument:
                                    'https://example.com/contractor1/gstin.pdf',
                            },
                            {
                                id: '1122334455',
                                aadhaarNumber: '112233445566',
                                nameOnAadhaar: 'Rajesh Kumar',
                                dateOfBirth: '1995-01-10T00:00:00.000Z',
                                address: '789 Builder Rd, Bangalore, Karnataka',
                                panNumber: '**********',
                                panName: 'Rajesh Kumar',
                                panDateOfBirth: '1995-01-10T00:00:00.000Z',
                                gstin: '12**********1Z5',
                                businessName: 'Kumar Builders',
                                experience: 8,
                                expertiseAreas: 'Residential, Commercial',
                                pastProjects:
                                    'Sunset Villas, Tech Park Phase 2',
                                status: 'approved',
                                aadhaarDocument:
                                    'https://example.com/contractor2/aadhaar.png',
                                panDocument:
                                    'https://example.com/contractor2/pan.png',
                                gstinDocument:
                                    'https://example.com/contractor2/gstin.pdf',
                            },
                        ],
                    },
                };

                // Filter applications based on role
                let filteredApplications = { brokers: [], contractors: [] };
                if (userData.role.toLowerCase() === 'contractor') {
                    filteredApplications.contractors =
                        response.data.contractors.filter(
                            (app) => app.nameOnAadhaar === userData.name
                        );
                } else if (userData.role.toLowerCase() === 'broker') {
                    filteredApplications.brokers = response.data.brokers.filter(
                        (app) => app.nameOnAadhaar === userData.name
                    );
                } else {
                    filteredApplications = {
                        brokers: response.data.brokers || [],
                        contractors: response.data.contractors || [],
                    };
                }

                setApplications(filteredApplications);
                Vibration.vibrate(100);
            } catch (error) {
                console.error('Fetch data error:', error);
                showToast('error', 'Error', 'Failed to fetch applications.');
                Vibration.vibrate(200);
            } finally {
                setIsFetching(false);
            }
        };
        fetchData();
    }, []);

    // Handle preview link
    const handlePreview = (url, documentType) => {
        Vibration.vibrate(50);
        if (url && url.startsWith('http')) {
            setModalDocument({ url, type: documentType });
            setModalVisible(true);
        } else {
            showToast(
                'error',
                'Error',
                `${documentType} document not available or invalid.`
            );
        }
    };

    // Handle edit
    const handleEdit = (type, data) => {
        Vibration.vibrate(50);
        router.push({
            pathname:
                type === 'broker'
                    ? '/Broker/BrokerForm'
                    : '/Contractors/ContractorForm',
            params: { editData: JSON.stringify(data) },
        });
    };

    // Get status badge style
    const getStatusStyle = (status) => {
        switch (status.toLowerCase()) {
            case 'approved':
                return [styles.statusBadge, styles.approvedBadge];
            case 'pending':
                return [styles.statusBadge, styles.pendingBadge];
            case 'rejected':
                return [styles.statusBadge, styles.rejectedBadge];
            default:
                return styles.statusBadge;
        }
    };

    if (isFetching) {
        return (
            <View
                style={[
                    styles.loadingContainer,
                    { backgroundColor: theme.BACKGROUND },
                ]}
            >
                <ActivityIndicator size="large" color={theme.PRIMARY} />
            </View>
        );
    }

    return (
        <View style={[styles.safe, { backgroundColor: theme.BACKGROUND }]}>
            <StatusBar
                barStyle={isDarkMode ? 'dark-content' : 'light-content'}
                backgroundColor={theme.BACKGROUND}
            />
            <ScrollView contentContainerStyle={styles.scrollContainer}>
                <View style={styles.headerContainer}>
                    <BackButton color={theme.WHITE} />
                </View>
                <View style={styles.backgroundContainer}>
                    <Image
                        source={require('../../assets/images/background.png')}
                        style={styles.backgroundImage}
                        resizeMode="cover"
                    />
                    <LinearGradient
                        colors={['rgba(42, 142, 158, 0.7)', theme.PRIMARY]}
                        style={styles.backgroundOverlay}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 0, y: 1 }}
                    />
                </View>
                <View style={styles.contentContainer}>
                    <View
                        style={[
                            styles.cardContainer,
                            { backgroundColor: theme.CARD + 'CC' },
                        ]}
                    >
                        {/* Broker Applications */}
                        {user.role.toLowerCase() !== 'contractor' && (
                            <>
                                <Text
                                    style={[
                                        styles.sectionTitle,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    Broker Applications
                                </Text>
                                {applications.brokers.length === 0 ? (
                                    <Text
                                        style={[
                                            styles.noDataText,
                                            { color: theme.TEXT_SECONDARY },
                                        ]}
                                    >
                                        No broker applications found.
                                    </Text>
                                ) : (
                                    applications.brokers.map((broker) => (
                                        <View
                                            key={broker.id}
                                            style={[
                                                styles.applicationCard,
                                                { backgroundColor: theme.CARD },
                                            ]}
                                            accessible={true}
                                            accessibilityLabel={`Broker application for ${broker.nameOnAadhaar}`}
                                            accessibilityRole="button"
                                        >
                                            <View style={styles.cardContent}>
                                                <View style={styles.cardHeader}>
                                                    <Text
                                                        style={[
                                                            styles.cardTitle,
                                                            {
                                                                color: theme.TEXT_PRIMARY,
                                                            },
                                                        ]}
                                                    >
                                                        {broker.nameOnAadhaar}
                                                    </Text>
                                                    <View
                                                        style={getStatusStyle(
                                                            broker.status
                                                        )}
                                                    >
                                                        <Text
                                                            style={[
                                                                styles.statusText,
                                                                {
                                                                    color: theme.WHITE,
                                                                },
                                                            ]}
                                                        >
                                                            {broker.status
                                                                .charAt(0)
                                                                .toUpperCase() +
                                                                broker.status.slice(
                                                                    1
                                                                )}
                                                        </Text>
                                                    </View>
                                                </View>
                                                <Text
                                                    style={[
                                                        styles.cardDetail,
                                                        {
                                                            color: theme.TEXT_SECONDARY,
                                                        },
                                                    ]}
                                                >
                                                    Aadhaar:{' '}
                                                    {broker.aadhaarNumber}
                                                </Text>
                                                <Text
                                                    style={[
                                                        styles.cardDetail,
                                                        {
                                                            color: theme.TEXT_SECONDARY,
                                                        },
                                                    ]}
                                                >
                                                    PAN: {broker.panNumber}
                                                </Text>
                                                <Text
                                                    style={[
                                                        styles.cardDetail,
                                                        {
                                                            color: theme.TEXT_SECONDARY,
                                                        },
                                                    ]}
                                                >
                                                    Address: {broker.address}
                                                </Text>
                                                <View
                                                    style={styles.previewLinks}
                                                >
                                                    <TouchableOpacity
                                                        onPress={() =>
                                                            handlePreview(
                                                                broker.aadhaarDocument,
                                                                'Aadhaar'
                                                            )
                                                        }
                                                        style={
                                                            styles.previewButton
                                                        }
                                                        accessibilityLabel="Preview Aadhaar document"
                                                        accessibilityRole="button"
                                                        testID={`preview-aadhaar-${broker.id}`}
                                                    >
                                                        <Text
                                                            style={[
                                                                styles.previewText,
                                                                {
                                                                    color: theme.PRIMARY,
                                                                },
                                                            ]}
                                                        >
                                                            Preview Aadhaar
                                                        </Text>
                                                    </TouchableOpacity>
                                                    <TouchableOpacity
                                                        onPress={() =>
                                                            handlePreview(
                                                                broker.panDocument,
                                                                'PAN'
                                                            )
                                                        }
                                                        style={
                                                            styles.previewButton
                                                        }
                                                        accessibilityLabel="Preview PAN document"
                                                        accessibilityRole="button"
                                                        testID={`preview-pan-${broker.id}`}
                                                    >
                                                        <Text
                                                            style={[
                                                                styles.previewText,
                                                                {
                                                                    color: theme.PRIMARY,
                                                                },
                                                            ]}
                                                        >
                                                            Preview PAN
                                                        </Text>
                                                    </TouchableOpacity>
                                                </View>
                                            </View>
                                            <TouchableOpacity
                                                onPress={() =>
                                                    handleEdit('broker', broker)
                                                }
                                                style={styles.editButton}
                                                accessibilityLabel="Edit broker application"
                                                accessibilityRole="button"
                                                testID={`edit-broker-${broker.id}`}
                                            >
                                                <Ionicons
                                                    name="pencil-outline"
                                                    size={20}
                                                    color={theme.PRIMARY}
                                                />
                                            </TouchableOpacity>
                                        </View>
                                    ))
                                )}
                            </>
                        )}

                        {/* Contractor Applications */}
                        {user.role.toLowerCase() !== 'site scout' && (
                            <>
                                <Text
                                    style={[
                                        styles.sectionTitle,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    Contractor Applications
                                </Text>
                                {applications.contractors.length === 0 ? (
                                    <Text
                                        style={[
                                            styles.noDataText,
                                            { color: theme.TEXT_SECONDARY },
                                        ]}
                                    >
                                        No contractor applications found.
                                    </Text>
                                ) : (
                                    applications.contractors.map(
                                        (contractor) => (
                                            <View
                                                key={contractor.id}
                                                style={[
                                                    styles.applicationCard,
                                                    {
                                                        backgroundColor:
                                                            theme.CARD,
                                                    },
                                                ]}
                                                accessible={true}
                                                accessibilityLabel={`Contractor application for ${contractor.nameOnAadhaar}`}
                                                accessibilityRole="button"
                                            >
                                                <View
                                                    style={styles.cardContent}
                                                >
                                                    <View
                                                        style={
                                                            styles.cardHeader
                                                        }
                                                    >
                                                        <Text
                                                            style={[
                                                                styles.cardTitle,
                                                                {
                                                                    color: theme.TEXT_PRIMARY,
                                                                },
                                                            ]}
                                                        >
                                                            {
                                                                contractor.nameOnAadhaar
                                                            }
                                                        </Text>
                                                        <View
                                                            style={getStatusStyle(
                                                                contractor.status
                                                            )}
                                                        >
                                                            <Text
                                                                style={[
                                                                    styles.statusText,
                                                                    {
                                                                        color: theme.WHITE,
                                                                    },
                                                                ]}
                                                            >
                                                                {contractor.status
                                                                    .charAt(0)
                                                                    .toUpperCase() +
                                                                    contractor.status.slice(
                                                                        1
                                                                    )}
                                                            </Text>
                                                        </View>
                                                    </View>
                                                    <Text
                                                        style={[
                                                            styles.cardDetail,
                                                            {
                                                                color: theme.TEXT_SECONDARY,
                                                            },
                                                        ]}
                                                    >
                                                        Aadhaar:{' '}
                                                        {
                                                            contractor.aadhaarNumber
                                                        }
                                                    </Text>
                                                    <Text
                                                        style={[
                                                            styles.cardDetail,
                                                            {
                                                                color: theme.TEXT_SECONDARY,
                                                            },
                                                        ]}
                                                    >
                                                        PAN:{' '}
                                                        {contractor.panNumber}
                                                    </Text>
                                                    <Text
                                                        style={[
                                                            styles.cardDetail,
                                                            {
                                                                color: theme.TEXT_SECONDARY,
                                                            },
                                                        ]}
                                                    >
                                                        GSTIN:{' '}
                                                        {contractor.gstin ||
                                                            'N/A'}
                                                    </Text>
                                                    <Text
                                                        style={[
                                                            styles.cardDetail,
                                                            {
                                                                color: theme.TEXT_SECONDARY,
                                                            },
                                                        ]}
                                                    >
                                                        Business:{' '}
                                                        {
                                                            contractor.businessName
                                                        }
                                                    </Text>
                                                    <Text
                                                        style={[
                                                            styles.cardDetail,
                                                            {
                                                                color: theme.TEXT_SECONDARY,
                                                            },
                                                        ]}
                                                    >
                                                        Experience:{' '}
                                                        {contractor.experience}{' '}
                                                        years
                                                    </Text>
                                                    <View
                                                        style={
                                                            styles.previewLinks
                                                        }
                                                    >
                                                        <TouchableOpacity
                                                            onPress={() =>
                                                                handlePreview(
                                                                    contractor.aadhaarDocument,
                                                                    'Aadhaar'
                                                                )
                                                            }
                                                            style={
                                                                styles.previewButton
                                                            }
                                                            accessibilityLabel="Preview Aadhaar document"
                                                            accessibilityRole="button"
                                                            testID={`preview-aadhaar-${contractor.id}`}
                                                        >
                                                            <Text
                                                                style={[
                                                                    styles.previewText,
                                                                    {
                                                                        color: theme.PRIMARY,
                                                                    },
                                                                ]}
                                                            >
                                                                Preview Aadhaar
                                                            </Text>
                                                        </TouchableOpacity>
                                                        <TouchableOpacity
                                                            onPress={() =>
                                                                handlePreview(
                                                                    contractor.panDocument,
                                                                    'PAN'
                                                                )
                                                            }
                                                            style={
                                                                styles.previewButton
                                                            }
                                                            accessibilityLabel="Preview PAN document"
                                                            accessibilityRole="button"
                                                            testID={`preview-pan-${contractor.id}`}
                                                        >
                                                            <Text
                                                                style={[
                                                                    styles.previewText,
                                                                    {
                                                                        color: theme.PRIMARY,
                                                                    },
                                                                ]}
                                                            >
                                                                Preview PAN
                                                            </Text>
                                                        </TouchableOpacity>
                                                        {contractor.gstinDocument && (
                                                            <TouchableOpacity
                                                                onPress={() =>
                                                                    handlePreview(
                                                                        contractor.gstinDocument,
                                                                        'GSTIN'
                                                                    )
                                                                }
                                                                style={
                                                                    styles.previewButton
                                                                }
                                                                accessibilityLabel="Preview GSTIN document"
                                                                accessibilityRole="button"
                                                                testID={`preview-gstin-${contractor.id}`}
                                                            >
                                                                <Text
                                                                    style={[
                                                                        styles.previewText,
                                                                        {
                                                                            color: theme.PRIMARY,
                                                                        },
                                                                    ]}
                                                                >
                                                                    Preview
                                                                    GSTIN
                                                                </Text>
                                                            </TouchableOpacity>
                                                        )}
                                                    </View>
                                                </View>
                                                <TouchableOpacity
                                                    onPress={() =>
                                                        handleEdit(
                                                            'contractor',
                                                            contractor
                                                        )
                                                    }
                                                    style={styles.editButton}
                                                    accessibilityLabel="Edit contractor application"
                                                    accessibilityRole="button"
                                                    testID={`edit-contractor-${contractor.id}`}
                                                >
                                                    <Ionicons
                                                        name="pencil-outline"
                                                        size={20}
                                                        color={theme.PRIMARY}
                                                    />
                                                </TouchableOpacity>
                                            </View>
                                        )
                                    )
                                )}
                            </>
                        )}
                    </View>
                </View>
            </ScrollView>
            <DocumentPreviewModal
                visible={modalVisible}
                documentUrl={modalDocument.url}
                documentType={modalDocument.type}
                onClose={() => setModalVisible(false)}
            />
        </View>
    );
}

const styles = StyleSheet.create({
    safe: {
        flex: 1,
    },
    scrollContainer: {
        flexGrow: 1,
    },
    headerContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingTop: 8,
    },
    themeToggle: {
        padding: 8,
    },
    backgroundContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: height * 0.6,
        zIndex: -1,
    },
    backgroundImage: {
        width: '100%',
        height: '100%',
    },
    backgroundOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    contentContainer: {
        flex: 1,
        alignItems: 'center',
    },
    logoContainer: {
        alignItems: 'center',
        marginTop: height * -0.045,
        marginBottom: height * 0.03,
    },
    logo: {
        width: 120,
        height: 120,
        borderWidth: 3,
        borderRadius: 60,
    },
    cardContainer: {
        width: '90%',
        maxWidth: 400,
        borderRadius: 20,
        padding: 24,
        marginBottom: 24,
    },
    sectionTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 16,
        marginTop: 8,
    },
    applicationCard: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        borderRadius: 12,
        padding: 16,
        marginBottom: 16,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    cardContent: {
        flex: 1,
        paddingRight: 12,
    },
    cardHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    cardTitle: {
        fontSize: 18,
        fontWeight: 'bold',
    },
    cardDetail: {
        fontSize: 14,
        marginBottom: 4,
    },
    previewLinks: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        marginTop: 8,
    },
    previewButton: {
        marginRight: 12,
        marginBottom: 8,
    },
    previewText: {
        fontSize: 14,
        textDecorationLine: 'underline',
    },
    editButton: {
        padding: 12,
        alignSelf: 'flex-start',
    },
    statusBadge: {
        paddingVertical: 4,
        paddingHorizontal: 8,
        borderRadius: 12,
    },
    approvedBadge: {
        backgroundColor: '#4CAF50',
    },
    pendingBadge: {
        backgroundColor: '#FFC107',
    },
    rejectedBadge: {
        backgroundColor: '#F44336',
    },
    statusText: {
        fontSize: 12,
        fontWeight: 'bold',
    },
    noDataText: {
        fontSize: 16,
        textAlign: 'center',
        marginBottom: 24,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
});
