import React, { useRef, useEffect, useContext } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    Animated,
    Easing,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { ThemeContext } from '../../context/ThemeContext';

const BrokerSuccess = () => {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();

    // Animation values
    const scaleAnim = useRef(new Animated.Value(0.7)).current;
    const fadeAnim = useRef(new Animated.Value(0)).current;

    useEffect(() => {
        Animated.parallel([
            Animated.spring(scaleAnim, {
                toValue: 1,
                friction: 7,
                tension: 60,
                useNativeDriver: true,
            }),
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 600,
                easing: Easing.out(Easing.exp),
                useNativeDriver: true,
            }),
        ]).start();
    }, [fadeAnim, scaleAnim]);

    return (
        <LinearGradient
            colors={[theme.SECONDARY, theme.PRIMARY]}
            start={{ x: 0.2, y: 0.1 }}
            end={{ x: 0.5, y: 0.9 }}
            style={styles.container}
        >
            <View style={styles.content}>
                <Animated.View
                    style={[
                        styles.iconWrapper,
                        { shadowColor: theme.PRIMARY },
                        {
                            transform: [{ scale: scaleAnim }],
                            opacity: fadeAnim,
                        },
                    ]}
                >
                    <Ionicons
                        name="checkmark-circle"
                        size={90}
                        color={theme.WHITE}
                    />
                </Animated.View>
                <Animated.Text
                    style={[
                        styles.title,
                        { color: theme.WHITE },
                        { opacity: fadeAnim },
                    ]}
                >
                    Application Submitted!
                </Animated.Text>
                <Animated.Text
                    style={[
                        styles.subtitle,
                        { color: theme.WHITE },
                        { opacity: fadeAnim },
                    ]}
                >
                    Your Site Scout application is pending verification.
                </Animated.Text>
                <TouchableOpacity
                    onPress={() => router.push('/Home')}
                    activeOpacity={0.85}
                    style={styles.button}
                >
                    <LinearGradient
                        colors={[theme.PRIMARY, theme.SECONDARY]}
                        style={styles.buttonGradient}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                    >
                        <Text
                            style={[styles.buttonText, { color: theme.WHITE }]}
                        >
                            Back to Home
                        </Text>
                    </LinearGradient>
                </TouchableOpacity>
            </View>
        </LinearGradient>
    );
};

const styles = StyleSheet.create({
    container: { flex: 1 },
    content: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 16,
    },
    iconWrapper: {
        marginBottom: 28,
        shadowOffset: { width: 0, height: 6 },
        shadowOpacity: 0.18,
        shadowRadius: 16,
        elevation: 8,
        backgroundColor: 'rgba(255,255,255,0.10)',
        borderRadius: 60,
        padding: 12,
    },
    title: {
        fontSize: 28,
        fontWeight: 'bold',
        marginBottom: 16,
        textAlign: 'center',
    },
    subtitle: {
        fontSize: 16,
        textAlign: 'center',
        marginBottom: 32,
    },
    button: { marginTop: 10 },
    buttonGradient: {
        borderRadius: 12,
        paddingHorizontal: 28,
        paddingVertical: 14,
    },
    buttonText: { fontSize: 16, fontWeight: '600' },
});

export default BrokerSuccess;
